/**
 * Mock数据库实现 - 临时替代腾讯云云开发
 * 用于本地调试和演示
 */

// 模拟数据存储
const mockData = {
  users: [
    {
      _id: 'user_001',
      username: 'teacher1',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password123
      name: '张老师',
      email: '<EMAIL>',
      role: 'teacher',
      department: '财务学院',
      status: 'active',
      created_at: new Date()
    },
    {
      _id: 'user_002',
      username: 'student1',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password123
      name: '李小明',
      student_id: '2024001',
      email: '<EMAIL>',
      role: 'student',
      class_id: 'class1',
      status: 'active',
      created_at: new Date()
    },
    {
      _id: 'user_003',
      username: 'student2',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password123
      name: '王小红',
      student_id: '2024002',
      email: '<EMAIL>',
      role: 'student',
      class_id: 'class1',
      status: 'active',
      created_at: new Date()
    }
  ],
  classes: [
    {
      _id: 'class_001',
      class_name: 'RPA实训班2024秋季',
      description: 'RPA财务机器人实训班，学习各种财务自动化场景',
      teacher_id: 'teacher1',
      max_students: 50,
      status: 'active',
      created_at: new Date()
    }
  ],
  training_scenarios: [
    {
      _id: 'scenario_001',
      id: 1,
      title: '企业网银流水查询下载',
      code: 'bank-statement-download', 
      description: '学习企业网银操作流程，掌握流水查询和批量下载技能',
      module: 1,
      module_name: '基础操作',
      difficulty: '初级',
      estimated_time: 30,
      validation_rules: {
        required_elements: ['login-form', 'query-form', 'download-btn'],
        success_criteria: 'download_completed',
        score_rules: {
          login: 20,
          query: 30, 
          download: 50
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_002',
      id: 2,
      title: '批量开具电子发票',
      code: 'invoice-generation',
      description: '从Excel读取订单信息，在开票系统中为每笔订单开具发票',
      module: 1,
      module_name: '基础操作',
      difficulty: '初级',
      estimated_time: 45,
      validation_rules: {
        required_elements: ['excel-upload', 'invoice-form', 'batch-process'],
        success_criteria: 'all_invoices_generated',
        score_rules: {
          upload: 20,
          process: 40,
          complete: 40
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_003',
      id: 3,
      title: '固定资产卡片信息核对',
      code: 'asset-verification',
      description: '根据Excel清单，在固资系统查询并核对资产信息，将结果写回Excel',
      module: 1,
      module_name: '基础操作',
      difficulty: '初级',
      estimated_time: 40,
      validation_rules: {
        required_elements: ['asset-query', 'data-compare', 'excel-update'],
        success_criteria: 'verification_completed',
        score_rules: {
          query: 30,
          compare: 35,
          update: 35
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_004',
      id: 4,
      title: '税务申报期提醒与状态核查',
      code: 'tax-reminder',
      description: '登录电子税务局，抓取本月申报日历，并核查上月各税种申报状态',
      module: 1,
      module_name: '基础操作',
      difficulty: '初级',
      estimated_time: 35,
      validation_rules: {
        required_elements: ['tax-login', 'calendar-scrape', 'status-check'],
        success_criteria: 'status_checked',
        score_rules: {
          login: 25,
          scrape: 35,
          check: 40
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_005',
      id: 5,
      title: '应收账款对账与核销',
      code: 'receivable-reconciliation',
      description: '整合销售系统订单和银行流水数据，匹配后在财务系统中核销',
      module: 2,
      module_name: '流程整合',
      difficulty: '中级',
      estimated_time: 60,
      validation_rules: {
        required_elements: ['data-integration', 'matching', 'reconciliation'],
        success_criteria: 'reconciliation_completed',
        score_rules: {
          integration: 30,
          matching: 40,
          reconciliation: 30
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_006',
      id: 6,
      title: '增值税进项税发票认证',
      code: 'vat-authentication',
      description: '从CSV文件读取发票信息，在增值税平台进行批量勾选认证',
      module: 2,
      module_name: '流程整合',
      difficulty: '中级',
      estimated_time: 50,
      validation_rules: {
        required_elements: ['csv-read', 'batch-select', 'authentication'],
        success_criteria: 'authentication_completed',
        score_rules: {
          read: 25,
          select: 35,
          auth: 40
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_007',
      id: 7,
      title: '月度工资条数据计算与生成',
      code: 'payroll-generation',
      description: '整合考勤表和绩效表，根据规则计算薪酬，为每人生成独立工资条',
      module: 2,
      module_name: '流程整合',
      difficulty: '中级',
      estimated_time: 55,
      validation_rules: {
        required_elements: ['data-merge', 'calculation', 'file-generation'],
        success_criteria: 'payroll_generated',
        score_rules: {
          merge: 30,
          calc: 40,
          generate: 30
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_008',
      id: 8,
      title: '财务报表数据自动汇总',
      code: 'financial-consolidation',
      description: '登录多个子公司系统，抓取关键财务数据，汇总到集团合并报表模板',
      module: 2,
      module_name: '流程整合',
      difficulty: '中级',
      estimated_time: 65,
      validation_rules: {
        required_elements: ['multi-login', 'data-extract', 'consolidation'],
        success_criteria: 'consolidation_completed',
        score_rules: {
          login: 25,
          extract: 40,
          consolidate: 35
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_009',
      id: 9,
      title: '供应商发票批量验真与入账(OCR)',
      code: 'invoice-ocr-verification',
      description: '用OCR识别发票图片，提取信息录入系统验真，成功后生成会计分录',
      module: 3,
      module_name: '智能应用',
      difficulty: '高级',
      estimated_time: 70,
      validation_rules: {
        required_elements: ['ocr-recognition', 'verification', 'accounting-entry'],
        success_criteria: 'entries_generated',
        score_rules: {
          ocr: 30,
          verify: 35,
          entry: 35
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_010',
      id: 10,
      title: '员工差旅费报销智能初审(人机协作)',
      code: 'travel-expense-review',
      description: '根据报销制度对报销单进行预审，合规通过，不合规或存疑则转交人工',
      module: 3,
      module_name: '智能应用',
      difficulty: '高级',
      estimated_time: 75,
      validation_rules: {
        required_elements: ['rule-engine', 'auto-review', 'human-handoff'],
        success_criteria: 'review_completed',
        score_rules: {
          rules: 35,
          review: 35,
          handoff: 30
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_011',
      id: 11,
      title: '合同关键信息提取与印花税计算申报(OCR)',
      code: 'contract-tax-calculation',
      description: '用OCR识别合同PDF，提取金额、类型等，根据税率表计算印花税并填报',
      module: 3,
      module_name: '智能应用',
      difficulty: '高级',
      estimated_time: 80,
      validation_rules: {
        required_elements: ['pdf-ocr', 'tax-calculation', 'filing'],
        success_criteria: 'tax_filed',
        score_rules: {
          ocr: 30,
          calc: 40,
          filing: 30
        }
      },
      is_active: true
    },
    {
      _id: 'scenario_012',
      id: 12,
      title: '自动生成总账科目余额调节表',
      code: 'balance-reconciliation',
      description: '分别获取银行对账单和系统日记账余额，结合未达账项清单，自动编制余额调节表',
      module: 3,
      module_name: '智能应用',
      difficulty: '高级',
      estimated_time: 85,
      validation_rules: {
        required_elements: ['data-extraction', 'balance-compare', 'reconciliation-table'],
        success_criteria: 'reconciliation_completed',
        score_rules: {
          extract: 30,
          compare: 35,
          table: 35
        }
      },
      is_active: true
    }
  ],
  student_progress: [],
  student_submissions: []
};

// Mock集合类
class MockCollection {
  constructor(name) {
    this.name = name;
  }

  async get() {
    console.log(`Mock: 查询集合 ${this.name}`);
    return {
      data: mockData[this.name] || []
    };
  }

  async add(data) {
    console.log(`Mock: 向集合 ${this.name} 添加数据`, data);
    const items = Array.isArray(data) ? data : [data];
    items.forEach((item, index) => {
      item._id = `${this.name}_${Date.now()}_${index}`;
    });
    mockData[this.name] = mockData[this.name] || [];
    mockData[this.name].push(...items);
    return { data: items };
  }

  where(query) {
    return {
      get: async () => {
        console.log(`Mock: 条件查询集合 ${this.name}`, query);
        let results = mockData[this.name] || [];
        
        // 扩展的查询逻辑
        Object.keys(query).forEach(key => {
          const value = query[key];
          if (value !== undefined) {
            results = results.filter(item => {
              if (item[key] === value) return true;
              // 支持复杂匹配
              if (key === 'role' && item.role === value) return true;
              if (key === 'status' && item.status === value) return true;
              if (key === 'is_active' && item.is_active === value) return true;
              if (key === 'id' && item.id === value) return true;
              if (key === 'student_id' && item.student_id === value) return true;
              if (key === 'scenario_id' && item.scenario_id === value) return true;
              return false;
            });
          }
        });
        
        return { data: results };
      },
      orderBy: (field, direction = 'asc') => {
        return {
          get: async () => {
            console.log(`Mock: 排序查询集合 ${this.name}`, { field, direction });
            let results = mockData[this.name] || [];
            
            // 应用where条件
            Object.keys(query).forEach(key => {
              const value = query[key];
              if (value !== undefined) {
                results = results.filter(item => item[key] === value);
              }
            });
            
            // 排序
            results.sort((a, b) => {
              if (direction === 'asc') {
                return a[field] > b[field] ? 1 : -1;
              } else {
                return a[field] < b[field] ? 1 : -1;
              }
            });
            
            return { data: results };
          }
        };
      }
    };
  }

  orderBy(field, direction = 'asc') {
    return {
      get: async () => {
        console.log(`Mock: 排序查询集合 ${this.name}`, { field, direction });
        let results = mockData[this.name] || [];
        
        results.sort((a, b) => {
          if (direction === 'asc') {
            return a[field] > b[field] ? 1 : -1;
          } else {
            return a[field] < b[field] ? 1 : -1;
          }
        });
        
        return { data: results };
      }
    };
  }

  doc(id) {
    return {
      get: async () => {
        console.log(`Mock: 获取文档 ${id} 从集合 ${this.name}`);
        const collection = mockData[this.name] || [];
        const item = collection.find(item => item._id === id);
        return { data: item ? [item] : [] };
      },
      update: async (updateData) => {
        console.log(`Mock: 更新文档 ${id} 在集合 ${this.name}`, updateData);
        const collection = mockData[this.name] || [];
        const index = collection.findIndex(item => item._id === id);
        if (index > -1) {
          collection[index] = { ...collection[index], ...updateData };
          return { data: collection[index] };
        }
        throw new Error(`Document ${id} not found`);
      },
      remove: async () => {
        console.log(`Mock: 删除文档 ${id} 从集合 ${this.name}`);
        const collection = mockData[this.name] || [];
        const index = collection.findIndex(item => item._id === id);
        if (index > -1) {
          collection.splice(index, 1);
        }
      }
    };
  }
}

// Mock数据库对象
const mockDb = {
  collection: (name) => new MockCollection(name),
  command: {
    in: (values) => ({ $in: values })
  }
};

// 数据库集合名称常量
const COLLECTIONS = {
  USERS: 'users',
  CLASSES: 'classes', 
  SCENARIOS: 'training_scenarios',
  PROGRESS: 'student_progress',
  SUBMISSIONS: 'student_submissions'
};

/**
 * 初始化数据库集合和基础数据
 */
async function initDatabase() {
  try {
    console.log('🎭 使用Mock数据库初始化...');
    console.log('✅ Mock数据库初始化完成');
    console.log(`📊 用户数量: ${mockData.users.length}`);
    console.log(`📚 班级数量: ${mockData.classes.length}`);
    console.log(`🎯 场景数量: ${mockData.training_scenarios.length}`);
  } catch (error) {
    console.error('Mock数据库初始化失败:', error);
    throw error;
  }
}

module.exports = {
  db: mockDb,
  tcb: null, // Mock中不需要
  COLLECTIONS,
  initDatabase
};