{"name": "rpa-training-platform", "version": "1.1.0", "description": "RPA财务机器人入门与进阶实训平台", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run lint && npm run test && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "deploy": "tcb functions:deploy", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write .", "precommit": "npm run lint && npm run test", "validate": "npm run lint && npm run test && npm run build"}, "keywords": ["RPA", "教育", "财务", "实训"], "author": "chan<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.4.0", "eslint-plugin-promise": "^6.1.1", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "supertest": "^6.3.3"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}}